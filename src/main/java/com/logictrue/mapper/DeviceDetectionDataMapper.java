package com.logictrue.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.logictrue.iot.entity.DeviceDetectionData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 设备检测数据Mapper接口
 */
@Mapper
public interface DeviceDetectionDataMapper extends BaseMapper<DeviceDetectionData> {

    /**
     * 更新解析状态
     */
    @Update("UPDATE device_detection_data SET " +
            "parse_status = #{parseStatus}, parse_message = #{parseMessage}, " +
            "parse_time = #{parseTime}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int updateParseStatus(@Param("id") Long id,
                         @Param("parseStatus") Integer parseStatus,
                         @Param("parseMessage") String parseMessage,
                         @Param("parseTime") LocalDateTime parseTime,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 更新解析完成后的统计信息
     */
    @Update("UPDATE device_detection_data SET " +
            "parse_status = #{parseStatus}, parse_message = #{parseMessage}, " +
            "parse_time = #{parseTime}, total_sheets = #{totalSheets}, " +
            "parsed_sheets = #{parsedSheets}, basic_fields_count = #{basicFieldsCount}, " +
            "table_rows_count = #{tableRowsCount}, update_by = #{updateBy}, " +
            "update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int updateAfterParsing(@Param("id") Long id,
                          @Param("parseStatus") Integer parseStatus,
                          @Param("parseMessage") String parseMessage,
                          @Param("parseTime") LocalDateTime parseTime,
                          @Param("totalSheets") Integer totalSheets,
                          @Param("parsedSheets") Integer parsedSheets,
                          @Param("basicFieldsCount") Integer basicFieldsCount,
                          @Param("tableRowsCount") Integer tableRowsCount,
                          @Param("updateBy") String updateBy,
                          @Param("updateTime") LocalDateTime updateTime);

    /**
     * 分页查询设备检测数据
     */
    @Select("SELECT * FROM device_detection_data " +
            "ORDER BY create_time DESC " +
            "LIMIT #{pageSize} OFFSET #{offset}")
    List<DeviceDetectionData> selectPageData(@Param("offset") int offset,
                                           @Param("pageSize") int pageSize);

    /**
     * 根据设备编码查询
     */
    @Select("SELECT * FROM device_detection_data " +
            "WHERE device_code = #{deviceCode} " +
            "ORDER BY create_time DESC")
    List<DeviceDetectionData> selectByDeviceCode(@Param("deviceCode") String deviceCode);

    /**
     * 根据解析状态查询
     */
    @Select("SELECT * FROM device_detection_data " +
            "WHERE parse_status = #{parseStatus} " +
            "ORDER BY create_time DESC")
    List<DeviceDetectionData> selectByParseStatus(@Param("parseStatus") Integer parseStatus);

    /**
     * 统计总记录数
     */
    @Select("SELECT COUNT(*) FROM device_detection_data")
    long countTotal();

    /**
     * 根据条件统计记录数
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM device_detection_data " +
            "WHERE 1=1 " +
            "<if test='deviceCode != null and deviceCode != \"\"'>" +
            "AND device_code LIKE CONCAT('%', #{deviceCode}, '%') " +
            "</if>" +
            "<if test='parseStatus != null'>" +
            "AND parse_status = #{parseStatus} " +
            "</if>" +
            "</script>")
    long countByCondition(@Param("deviceCode") String deviceCode,
                         @Param("parseStatus") Integer parseStatus);

    /**
     * 根据设备ID和文件路径查找最新记录
     */
    @Select("SELECT * FROM device_detection_data " +
            "WHERE device_code = #{deviceCode} AND file_path = #{filePath} " +
            "ORDER BY create_time DESC " +
            "LIMIT 1")
    DeviceDetectionData selectLatestByDeviceAndFile(@Param("deviceCode") String deviceCode,
                                                   @Param("filePath") String filePath);

    /**
     * 更新collectData字段
     */
    @Update("UPDATE device_detection_data SET " +
            "collect_data = #{collectData}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int updateCollectData(@Param("id") Long id,
                         @Param("collectData") String collectData,
                         @Param("updateTime") LocalDateTime updateTime);

    /**
     * 解析成功后更新记录（包含解析状态、表单数据和文件路径）
     */
    @Update("UPDATE device_detection_data SET " +
            "parse_status = #{parseStatus}, " +
            "parse_message = #{parseMessage}, " +
            "collect_data = #{collectData}, " +
            "collect_path = #{collectPath}, " +
            "update_by = #{updateBy}, " +
            "update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int updateAfterParsingWithCollectData(@Param("id") Long id,
                                         @Param("parseStatus") Integer parseStatus,
                                         @Param("parseMessage") String parseMessage,
                                         @Param("collectData") String collectData,
                                         @Param("collectPath") String collectPath,
                                         @Param("updateBy") String updateBy,
                                         @Param("updateTime") LocalDateTime updateTime);
}
