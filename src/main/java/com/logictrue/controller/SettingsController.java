package com.logictrue.controller;

import com.logictrue.config.ConfigManager;
import com.logictrue.model.ExternalApp;
import com.logictrue.model.FormField;
import com.logictrue.service.ExternalAppService;
import com.logictrue.service.NetworkService;
import com.logictrue.util.AlertUtil;
import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.net.URL;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;

/**
 * 设置界面控制器
 */
public class SettingsController implements Initializable {
    private static final Logger logger = LoggerFactory.getLogger(SettingsController.class);

    @FXML
    private TextField deviceIdField;

    @FXML
    private TextField formNameField;

    @FXML
    private TextField apiUrlField;

    @FXML
    private TextField heartbeatUrlField;

    @FXML
    private TextField heartbeatIntervalField;

    @FXML
    private TextField imageUrlField;

    @FXML
    private TextField backgroundImagePathField;

    @FXML
    private Button applyBackgroundButton;

    // 新增的模板和Excel采集相关字段
    @FXML
    private TextField templateDownloadUrlField;

    @FXML
    private Button downloadTemplateButton;

    @FXML
    private TextField excelCollectionPathField;

    @FXML
    private Button browseExcelPathButton;

    @FXML
    private CheckBox autoExcelCollectionCheckBox;

    @FXML
    private TextField collectionIntervalField;

    // 主页标题相关字段
    @FXML
    private TextField mainPageTitleField;

    @FXML
    private TextField mainPageTitleFontSizeField;

    @FXML
    private TextField mainPageTitleColorField;

    @FXML
    private TextField mainPageTitleTopMarginField;

    @FXML
    private Label titlePreviewLabel;

    @FXML
    private Button applyTitleStyleButton;

    @FXML
    private Button downloadImageButton;

    @FXML
    private Button testHeartbeatButton;

    @FXML
    private Button saveButton;

    @FXML
    private Button cancelButton;

    @FXML
    private ProgressIndicator progressIndicator;

    @FXML
    private Label statusLabel;

    // 表单字段管理相关组件
    @FXML
    private TableView<FormField> formFieldsTable;

    @FXML
    private TableColumn<FormField, String> labelColumn;

    @FXML
    private TableColumn<FormField, String> nameColumn;

    @FXML
    private TableColumn<FormField, String> typeColumn;

    @FXML
    private TableColumn<FormField, String> requiredColumn;

    @FXML
    private Button addFieldButton;

    @FXML
    private Button editFieldButton;

    @FXML
    private Button deleteFieldButton;

    @FXML
    private Button moveUpButton;

    @FXML
    private Button moveDownButton;

    // 外部应用程序管理相关组件
    @FXML
    private TableView<ExternalApp> externalAppsTable;

    @FXML
    private TableColumn<ExternalApp, String> appNameColumn;

    @FXML
    private TableColumn<ExternalApp, String> appPathColumn;

    @FXML
    private TableColumn<ExternalApp, String> appDescriptionColumn;

    @FXML
    private Button addAppButton;

    @FXML
    private Button editAppButton;

    @FXML
    private Button deleteAppButton;

    @FXML
    private Button launchAppButton;

    // 配置文件管理相关组件
    @FXML
    private Label configPathLabel;

    @FXML
    private Label configSizeLabel;

    @FXML
    private Label configModifiedLabel;

    @FXML
    private Button exportConfigButton;

    @FXML
    private Button importConfigButton;

    @FXML
    private Button resetConfigButton;

    private ConfigManager configManager;
    private NetworkService networkService;
    private ExternalAppService externalAppService;
    private MainController mainController;
    private ObservableList<FormField> formFieldsList;
    private ObservableList<ExternalApp> externalAppsList;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        configManager = ConfigManager.getInstance();
        networkService = new NetworkService();
        externalAppService = new ExternalAppService();
        formFieldsList = FXCollections.observableArrayList();
        externalAppsList = FXCollections.observableArrayList();

        // 初始化界面
        initializeUI();

        // 初始化表单字段表格
        initializeFormFieldsTable();

        // 初始化外部应用程序表格
        initializeExternalAppsTable();


        // 加载当前配置
        loadCurrentConfig();

        // 加载表单字段
        loadFormFields();

        // 加载外部应用程序
        loadExternalApps();

        // 加载配置文件信息
        loadConfigFileInfo();

        // 测试数据
        testFormFieldsData();

        logger.info("设置界面初始化完成");
    }

    /**
     * 设置心跳间隔字段的验证
     */
    private void setupHeartbeatIntervalValidation() {
        // 只允许输入数字
        heartbeatIntervalField.textProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue.matches("\\d*")) {
                heartbeatIntervalField.setText(oldValue);
            }
        });

        // 失去焦点时验证范围
        heartbeatIntervalField.focusedProperty().addListener((observable, oldValue, newValue) -> {
            if (!newValue) { // 失去焦点
                validateHeartbeatInterval();
            }
        });
    }

    /**
     * 验证心跳间隔值
     */
    private void validateHeartbeatInterval() {
        String text = heartbeatIntervalField.getText().trim();
        if (text.isEmpty()) {
            heartbeatIntervalField.setText("30"); // 默认30秒
            return;
        }

        try {
            int interval = Integer.parseInt(text);
            if (interval < 5) {
                heartbeatIntervalField.setText("5"); // 最小5秒
                AlertUtil.showWarning("提示", "心跳间隔不能小于5秒，已自动调整为5秒", getOwnerWindow());
            } else if (interval > 3600) {
                heartbeatIntervalField.setText("3600"); // 最大1小时
                AlertUtil.showWarning("提示", "心跳间隔不能大于3600秒（1小时），已自动调整为3600秒", getOwnerWindow());
            }
        } catch (NumberFormatException e) {
            heartbeatIntervalField.setText("30"); // 默认30秒
            AlertUtil.showError("错误", "心跳间隔必须是数字，已重置为默认值30秒", getOwnerWindow());
        }
    }

    /**
     * 初始化UI组件
     */
    private void initializeUI() {
        // 隐藏进度指示器
        progressIndicator.setVisible(false);

        // 绑定事件
        downloadImageButton.setOnAction(event -> downloadDeviceImage());
        applyBackgroundButton.setOnAction(event -> applyBackgroundImage());
        testHeartbeatButton.setOnAction(event -> testHeartbeat());
        downloadTemplateButton.setOnAction(event -> downloadTemplate());
        browseExcelPathButton.setOnAction(event -> browseExcelCollectionPath());
        applyTitleStyleButton.setOnAction(event -> applyTitleStyleToMainPage());
        saveButton.setOnAction(event -> saveSettings());
        cancelButton.setOnAction(event -> closeWindow());

        // 设置心跳间隔字段的数字验证
        setupHeartbeatIntervalValidation();


        // 标题配置变化时更新预览
        setupTitlePreviewListeners();

        // 设置应用按钮样式
        if (applyTitleStyleButton != null) {
            applyTitleStyleButton.getStyleClass().add("apply-title-button");
        }

        // 心跳URL变化时启用测试按钮
        heartbeatUrlField.textProperty().addListener((observable, oldValue, newValue) -> {
            testHeartbeatButton.setDisable(newValue == null || newValue.trim().isEmpty());
        });

        // 表单字段管理按钮事件
        addFieldButton.setOnAction(event -> addFormField());
        editFieldButton.setOnAction(event -> editFormField());
        deleteFieldButton.setOnAction(event -> deleteFormField());
        moveUpButton.setOnAction(event -> moveFieldUp());
        moveDownButton.setOnAction(event -> moveFieldDown());

        // 外部应用程序管理按钮事件
        addAppButton.setOnAction(event -> addExternalApp());
        editAppButton.setOnAction(event -> editExternalApp());
        deleteAppButton.setOnAction(event -> deleteExternalApp());
        launchAppButton.setOnAction(event -> launchExternalApp());

        // 配置文件管理按钮事件
        exportConfigButton.setOnAction(event -> exportConfig());
        importConfigButton.setOnAction(event -> importConfig());
        resetConfigButton.setOnAction(event -> resetConfig());
    }

    /**
     * 设置标题预览监听器
     */
    private void setupTitlePreviewListeners() {
        if (mainPageTitleField != null && mainPageTitleFontSizeField != null &&
            mainPageTitleColorField != null && mainPageTitleTopMarginField != null &&
            titlePreviewLabel != null) {

            // 监听标题文本变化
            mainPageTitleField.textProperty().addListener((observable, oldValue, newValue) -> updateTitlePreview());

            // 监听字体大小变化
            mainPageTitleFontSizeField.textProperty().addListener((observable, oldValue, newValue) -> updateTitlePreview());

            // 监听颜色变化
            mainPageTitleColorField.textProperty().addListener((observable, oldValue, newValue) -> updateTitlePreview());

            // 监听顶部边距变化
            mainPageTitleTopMarginField.textProperty().addListener((observable, oldValue, newValue) -> updateTitlePreview());
        }
    }

    /**
     * 应用标题样式到主页面
     */
    private void applyTitleStyleToMainPage() {
        if (mainController == null) {
            showStatus("无法连接到主界面", false);
            return;
        }

        try {
            // 临时保存当前配置到ConfigManager（不写入文件）

            // 获取当前设置界面的配置值
            String titleText = mainPageTitleField.getText().trim();
            if (titleText.isEmpty()) {
                titleText = "欢迎使用IoT数据采集系统";
            }

            double fontSize = 24.0;
            try {
                fontSize = Double.parseDouble(mainPageTitleFontSizeField.getText().trim());
                if (fontSize <= 0) fontSize = 24.0;
            } catch (NumberFormatException e) {
                fontSize = 24.0;
            }

            String color = mainPageTitleColorField.getText().trim();
            if (color.isEmpty() || !color.startsWith("#")) {
                color = "#333333";
            }

            double topMargin = 50.0;
            try {
                topMargin = Double.parseDouble(mainPageTitleTopMarginField.getText().trim());
                if (topMargin < 0) topMargin = 50.0;
            } catch (NumberFormatException e) {
                topMargin = 50.0;
            }

            // 临时更新ConfigManager中的值（不保存到文件）
            configManager.getConfig().setMainPageTitle(titleText);
            configManager.getConfig().setMainPageTitleFontSize(fontSize);
            configManager.getConfig().setMainPageTitleColor(color);
            configManager.getConfig().setMainPageTitleTopMargin(topMargin);

            // 通知主界面更新标题
            mainController.updateMainPageTitle();

            showStatus("标题样式已应用到主页面", true);
            logger.info("临时应用标题样式: {}, 字体: {}, 颜色: {}, 边距: {}",
                titleText, fontSize, color, topMargin);

        } catch (Exception e) {
            logger.error("应用标题样式失败", e);
            showStatus("应用标题样式失败: " + e.getMessage(), false);
        }
    }

    /**
     * 更新标题预览
     */
    private void updateTitlePreview() {
        if (titlePreviewLabel == null) return;

        try {
            String titleText = mainPageTitleField.getText();
            if (titleText == null || titleText.trim().isEmpty()) {
                titleText = "预览标题";
            }

            double fontSize = 18.0; // 预览用较小字体
            try {
                double configFontSize = Double.parseDouble(mainPageTitleFontSizeField.getText().trim());
                fontSize = Math.max(configFontSize * 0.75, 12.0); // 预览字体为配置字体的75%
            } catch (NumberFormatException e) {
                // 使用默认字体大小
            }

            String color = mainPageTitleColorField.getText().trim();
            if (color.isEmpty() || !color.startsWith("#")) {
                color = "#333333";
            }

            double topMargin = 10.0; // 预览用较小边距
            try {
                double configMargin = Double.parseDouble(mainPageTitleTopMarginField.getText().trim());
                topMargin = Math.max(configMargin * 0.2, 5.0); // 预览边距为配置边距的20%
            } catch (NumberFormatException e) {
                // 使用默认边距
            }

            titlePreviewLabel.setText(titleText);
            titlePreviewLabel.setStyle(String.format(
                "-fx-font-size: %.1fpx; -fx-font-weight: bold; -fx-text-fill: %s; -fx-padding: %.1f 0 0 0;",
                fontSize, color, topMargin
            ));

        } catch (Exception e) {
            logger.warn("更新标题预览失败", e);
        }
    }

    /**
     * 加载当前配置
     */
    private void loadCurrentConfig() {
        deviceIdField.setText(configManager.getDeviceId());
        formNameField.setText(configManager.getFormName());
        apiUrlField.setText(configManager.getApiUrl());
        heartbeatUrlField.setText(configManager.getHeartbeatUrl());
        heartbeatIntervalField.setText(String.valueOf(configManager.getHeartbeatInterval()));
        imageUrlField.setText(configManager.getImageUrl());
        backgroundImagePathField.setText(configManager.getBackgroundImagePath());

        // 加载新增的配置项
        templateDownloadUrlField.setText(configManager.getTemplateDownloadUrl());
        excelCollectionPathField.setText(configManager.getExcelCollectionPath());
        autoExcelCollectionCheckBox.setSelected(configManager.isAutoExcelCollection());
        collectionIntervalField.setText(String.valueOf(configManager.getCollectionIntervalMinutes()));

        // 加载主页标题配置
        mainPageTitleField.setText(configManager.getMainPageTitle());
        mainPageTitleFontSizeField.setText(String.valueOf(configManager.getMainPageTitleFontSize()));
        mainPageTitleColorField.setText(configManager.getMainPageTitleColor());
        mainPageTitleTopMarginField.setText(String.valueOf(configManager.getMainPageTitleTopMargin()));

        // 更新标题预览
        updateTitlePreview();

        // 更新按钮状态
        downloadImageButton.setDisable(imageUrlField.getText().trim().isEmpty());
        testHeartbeatButton.setDisable(heartbeatUrlField.getText().trim().isEmpty());
    }

    /**
     * 下载设备图片
     */
    @FXML
    private void downloadDeviceImage() {
        String imageUrl = imageUrlField.getText().trim();
        if (imageUrl.isEmpty()) {
            showStatus("请先输入图片地址", false);
            return;
        }

        // 显示进度指示器
        progressIndicator.setVisible(true);
        downloadImageButton.setDisable(true);
        showStatus("正在下载设备图片...", true);

        Task<String> downloadTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                return networkService.downloadDeviceImage(imageUrl).get();
            }
        };

        downloadTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadImageButton.setDisable(false);

                String imagePath = downloadTask.getValue();
                if (imagePath != null) {
                    // 自动填入下载的图片路径到背景图片路径字段
                    backgroundImagePathField.setText(imagePath);
                    configManager.setBackgroundImagePath(imagePath);
                    showStatus("设备图片下载成功，已自动填入背景图片路径", true);

                    // 通知主界面更新背景图片
                    if (mainController != null) {
                        mainController.updateBackgroundImage(imagePath);
                    }
                } else {
                    showStatus("设备图片下载失败", false);
                }
            });
        });

        downloadTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadImageButton.setDisable(false);
                showStatus("设备图片下载失败: " + downloadTask.getException().getMessage(), false);
            });
        });

        Thread downloadThread = new Thread(downloadTask);
        downloadThread.setDaemon(true);
        downloadThread.start();
    }

    /**
     * 应用背景图片
     */
    @FXML
    private void applyBackgroundImage() {
        String backgroundImagePath = backgroundImagePathField.getText().trim();

        try {
            if (backgroundImagePath.isEmpty()) {
                // 背景图片路径为空时，使用默认图片
                configManager.setBackgroundImagePath("");
                showStatus("已应用默认背景图片", true);
                logger.info("应用默认背景图片");
            } else {
                // 检查文件是否存在
                File imageFile = new File(backgroundImagePath);
                if (!imageFile.exists()) {
                    showStatus("背景图片文件不存在: " + backgroundImagePath, false);
                    return;
                }

                // 检查是否为图片文件
                String fileName = imageFile.getName().toLowerCase();
                if (!fileName.endsWith(".jpg") && !fileName.endsWith(".jpeg") &&
                    !fileName.endsWith(".png") && !fileName.endsWith(".gif") &&
                    !fileName.endsWith(".bmp") && !fileName.endsWith(".webp")) {
                    showStatus("不支持的图片格式，请选择jpg、png、gif、bmp或webp格式的图片", false);
                    return;
                }

                // 保存背景图片路径到配置
                configManager.setBackgroundImagePath(backgroundImagePath);
                showStatus("背景图片路径已保存", true);
                logger.info("应用背景图片: {}", backgroundImagePath);
            }

            // 通知主界面更新背景图片
            if (mainController != null) {
                if (backgroundImagePath.isEmpty()) {
                    // 使用默认背景
                    mainController.refreshBackgroundImage();
                } else {
                    // 使用指定的背景图片
                    mainController.updateBackgroundImage(backgroundImagePath);
                }
            }

        } catch (Exception e) {
            logger.error("应用背景图片失败", e);
            showStatus("应用背景图片失败: " + e.getMessage(), false);
        }
    }

    /**
     * 测试心跳连接
     */
    @FXML
    private void testHeartbeat() {
        String heartbeatUrl = heartbeatUrlField.getText().trim();
        if (heartbeatUrl.isEmpty()) {
            showStatus("请先输入心跳地址", false);
            return;
        }

        // 临时更新配置以进行测试
        String originalUrl = configManager.getHeartbeatUrl();
        configManager.setHeartbeatUrl(heartbeatUrl);

        progressIndicator.setVisible(true);
        testHeartbeatButton.setDisable(true);
        showStatus("正在测试心跳连接...", true);

        networkService.sendHeartbeat().thenAccept(result -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                testHeartbeatButton.setDisable(false);

                if (result.isSuccess()) {
                    showStatus(String.format("心跳测试成功 (响应时间: %dms)", result.getResponseTime()), true);
                } else {
                    showStatus(String.format("心跳测试失败 (状态码: %d)", result.getStatusCode()), false);
                }
            });
        }).exceptionally(throwable -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                testHeartbeatButton.setDisable(false);
                showStatus("心跳测试异常: " + throwable.getMessage(), false);
            });
            return null;
        });

        // 恢复原始配置
        configManager.setHeartbeatUrl(originalUrl);
    }

    /**
     * 保存设置
     */
    @FXML
    private void saveSettings() {
        try {
            // 验证输入
            if (formNameField.getText().trim().isEmpty()) {
                showStatus("表单名称不能为空", false);
                return;
            }

            if (apiUrlField.getText().trim().isEmpty()) {
                showStatus("接口地址不能为空", false);
                return;
            }

            // 保存配置 - 分步保存以避免并发问题
            configManager.setDeviceId(deviceIdField.getText().trim());
            configManager.setFormName(formNameField.getText().trim());
            configManager.setApiUrl(apiUrlField.getText().trim());
            configManager.setHeartbeatUrl(heartbeatUrlField.getText().trim());

            // 保存心跳间隔，验证并设置默认值
            try {
                int heartbeatInterval = Integer.parseInt(heartbeatIntervalField.getText().trim());
                if (heartbeatInterval < 5) {
                    heartbeatInterval = 5; // 最小5秒
                    heartbeatIntervalField.setText("5");
                }
                configManager.setHeartbeatInterval(heartbeatInterval);
            } catch (NumberFormatException e) {
                configManager.setHeartbeatInterval(30); // 默认30秒
                heartbeatIntervalField.setText("30");
            }

            configManager.setImageUrl(imageUrlField.getText().trim());
            configManager.setBackgroundImagePath(backgroundImagePathField.getText().trim());

            // 保存新增的配置项
            configManager.setTemplateDownloadUrl(templateDownloadUrlField.getText().trim());
            configManager.setExcelCollectionPath(excelCollectionPathField.getText().trim());
            configManager.setAutoExcelCollection(autoExcelCollectionCheckBox.isSelected());

            // 保存采集间隔，处理可能的数字格式异常
            try {
                int interval = Integer.parseInt(collectionIntervalField.getText().trim());
                if (interval > 0) {
                    configManager.setCollectionIntervalMinutes(interval);
                } else {
                    configManager.setCollectionIntervalMinutes(30); // 默认30分钟
                }
            } catch (NumberFormatException e) {
                configManager.setCollectionIntervalMinutes(30); // 默认30分钟
                collectionIntervalField.setText("30");
            }

            // 保存主页标题配置
            configManager.setMainPageTitle(mainPageTitleField.getText().trim());

            // 保存标题字体大小，处理可能的数字格式异常
            try {
                double fontSize = Double.parseDouble(mainPageTitleFontSizeField.getText().trim());
                if (fontSize > 0) {
                    configManager.setMainPageTitleFontSize(fontSize);
                } else {
                    configManager.setMainPageTitleFontSize(24.0); // 默认24px
                }
            } catch (NumberFormatException e) {
                configManager.setMainPageTitleFontSize(24.0); // 默认24px
                mainPageTitleFontSizeField.setText("24.0");
            }

            // 保存标题颜色
            String titleColor = mainPageTitleColorField.getText().trim();
            if (titleColor.isEmpty() || !titleColor.startsWith("#")) {
                titleColor = "#333333"; // 默认颜色
                mainPageTitleColorField.setText(titleColor);
            }
            configManager.setMainPageTitleColor(titleColor);

            // 保存标题顶部边距，处理可能的数字格式异常
            try {
                double topMargin = Double.parseDouble(mainPageTitleTopMarginField.getText().trim());
                if (topMargin >= 0) {
                    configManager.setMainPageTitleTopMargin(topMargin);
                } else {
                    configManager.setMainPageTitleTopMargin(50.0); // 默认50px
                }
            } catch (NumberFormatException e) {
                configManager.setMainPageTitleTopMargin(50.0); // 默认50px
                mainPageTitleTopMarginField.setText("50.0");
            }

            // 保存表单字段配置
            logger.info("保存表单字段配置，共{}个字段", formFieldsList.size());
            configManager.setFormFields(new ArrayList<>(formFieldsList));

            // 保存外部应用程序配置
            logger.info("保存外部应用程序配置，共{}个应用", externalAppsList.size());
            configManager.setExternalApps(new ArrayList<>(externalAppsList));

            // 通知主界面刷新外部应用程序按钮和主页标题
            if (mainController != null) {
                mainController.refreshExternalAppButtons();
                mainController.updateMainPageTitle();
                // 重启心跳服务以应用新的心跳间隔配置
                mainController.restartHeartbeatService();
            }

            showStatus("设置保存成功", true);
            logger.info("设置保存成功");

            // 延迟关闭窗口
            javafx.animation.Timeline timeline = new javafx.animation.Timeline(
                new javafx.animation.KeyFrame(
                    javafx.util.Duration.seconds(1),
                    e -> closeWindow()
                )
            );
            timeline.play();

        } catch (Exception e) {
            logger.error("保存设置失败", e);
            showStatus("保存设置失败: " + e.getMessage(), false);
        }
    }

    /**
     * 关闭窗口
     */
    @FXML
    private void closeWindow() {
        // 如果用户点击取消，恢复主页面的原始标题样式
        if (mainController != null) {
            mainController.reloadTitleStyleFromConfig();
        }

        Stage stage = (Stage) cancelButton.getScene().getWindow();
        stage.close();
    }

    /**
     * 显示状态信息
     */
    private void showStatus(String message, boolean success) {
        statusLabel.setText(message);
        statusLabel.getStyleClass().clear();
        statusLabel.getStyleClass().add(success ? "status-success" : "status-error");
    }

    /**
     * 初始化表单字段表格
     */
    private void initializeFormFieldsTable() {
        // 设置表格列 - 使用lambda表达式确保正确获取属性值
        labelColumn.setCellValueFactory(cellData -> {
            FormField field = cellData.getValue();
            String label = field != null ? field.getLabel() : "";
            logger.debug("获取字段标签: {}", label);
            return new SimpleStringProperty(label);
        });

        nameColumn.setCellValueFactory(cellData -> {
            FormField field = cellData.getValue();
            String name = field != null ? field.getName() : "";
            logger.debug("获取字段名称: {}", name);
            return new SimpleStringProperty(name);
        });

        typeColumn.setCellValueFactory(cellData -> {
            FormField field = cellData.getValue();
            String typeName = field != null && field.getType() != null ?
                field.getType().getDisplayName() : "";
            logger.debug("获取字段类型: {}", typeName);
            return new SimpleStringProperty(typeName);
        });

        requiredColumn.setCellValueFactory(cellData -> {
            FormField field = cellData.getValue();
            String requiredText = field != null ? (field.isRequired() ? "是" : "否") : "否";
            logger.debug("获取字段必填状态: {}", requiredText);
            return new SimpleStringProperty(requiredText);
        });

        // 设置表格数据
        formFieldsTable.setItems(formFieldsList);

        // 表格选择事件
        formFieldsTable.getSelectionModel().selectedItemProperty().addListener((observable, oldValue, newValue) -> {
            boolean hasSelection = newValue != null;
            editFieldButton.setDisable(!hasSelection);
            deleteFieldButton.setDisable(!hasSelection);
            moveUpButton.setDisable(!hasSelection);
            moveDownButton.setDisable(!hasSelection);
        });

        logger.info("表单字段表格初始化完成");
    }

    /**
     * 加载表单字段
     */
    private void loadFormFields() {
        List<FormField> fields = configManager.getFormFields();
        logger.info("加载表单字段，共{}个字段", fields.size());
        for (FormField field : fields) {
            logger.info("字段信息: id={}, label={}, name={}, type={}",
                field.getId(), field.getLabel(), field.getName(), field.getType());
        }
        formFieldsList.clear();
        formFieldsList.addAll(fields);
        logger.info("表单字段列表更新完成，当前列表大小: {}", formFieldsList.size());

        // 强制刷新表格显示
        Platform.runLater(() -> {
            formFieldsTable.refresh();
            logger.info("表格刷新完成");
        });
    }

    /**
     * 新增表单字段
     */
    private void addFormField() {
        Stage currentStage = (Stage) cancelButton.getScene().getWindow();
        FormFieldEditDialog dialog = new FormFieldEditDialog(null, currentStage);
        if (dialog.showAndWait()) {
            FormField newField = dialog.getFormField();
            formFieldsList.add(newField);
            showStatus("字段添加成功", true);
        }
    }

    /**
     * 编辑表单字段
     */
    private void editFormField() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            Stage currentStage = (Stage) cancelButton.getScene().getWindow();
            FormFieldEditDialog dialog = new FormFieldEditDialog(selectedField, currentStage);
            if (dialog.showAndWait()) {
                // 刷新表格显示
                formFieldsTable.refresh();
                showStatus("字段编辑成功", true);
            }
        }
    }

    /**
     * 删除表单字段
     */
    private void deleteFormField() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            Alert alert = createAlert(Alert.AlertType.CONFIRMATION);
            alert.setTitle("确认删除");
            alert.setHeaderText(null);
            alert.setContentText("确定要删除字段 \"" + selectedField.getLabel() + "\" 吗？");

            Optional<ButtonType> result = alert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                formFieldsList.remove(selectedField);
                showStatus("字段删除成功", true);
            }
        }
    }

    /**
     * 上移字段
     */
    private void moveFieldUp() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            int currentIndex = formFieldsList.indexOf(selectedField);
            if (currentIndex > 0) {
                formFieldsList.remove(currentIndex);
                formFieldsList.add(currentIndex - 1, selectedField);
                formFieldsTable.getSelectionModel().select(currentIndex - 1);
                showStatus("字段上移成功", true);
            }
        }
    }

    /**
     * 下移字段
     */
    private void moveFieldDown() {
        FormField selectedField = formFieldsTable.getSelectionModel().getSelectedItem();
        if (selectedField != null) {
            int currentIndex = formFieldsList.indexOf(selectedField);
            if (currentIndex < formFieldsList.size() - 1) {
                formFieldsList.remove(currentIndex);
                formFieldsList.add(currentIndex + 1, selectedField);
                formFieldsTable.getSelectionModel().select(currentIndex + 1);
                showStatus("字段下移成功", true);
            }
        }
    }



    /**
     * 测试表单字段数据
     */
    private void testFormFieldsData() {
        logger.info("=== 测试表单字段数据 ===");
        logger.info("formFieldsList大小: {}", formFieldsList.size());
        for (int i = 0; i < formFieldsList.size(); i++) {
            FormField field = formFieldsList.get(i);
            logger.info("字段[{}]: id={}, label={}, name={}, type={}",
                i, field.getId(), field.getLabel(), field.getName(),
                field.getType() != null ? field.getType().getDisplayName() : "null");
        }
        logger.info("=== 测试完成 ===");
    }

    /**
     * 初始化外部应用程序表格
     */
    private void initializeExternalAppsTable() {
        // 设置表格列使用回调函数
        appNameColumn.setCellValueFactory(cellData -> {
            ExternalApp app = cellData.getValue();
            return new SimpleStringProperty(app != null ? app.getName() : "");
        });

        appPathColumn.setCellValueFactory(cellData -> {
            ExternalApp app = cellData.getValue();
            return new SimpleStringProperty(app != null ? app.getPath() : "");
        });

        appDescriptionColumn.setCellValueFactory(cellData -> {
            ExternalApp app = cellData.getValue();
            return new SimpleStringProperty(app != null ? app.getDescription() : "");
        });

        // 设置列的可调整大小
        appNameColumn.setResizable(true);
        appPathColumn.setResizable(true);
        appDescriptionColumn.setResizable(true);

        // 设置表格数据
        externalAppsTable.setItems(externalAppsList);

        // 设置表格属性
        externalAppsTable.setColumnResizePolicy(TableView.CONSTRAINED_RESIZE_POLICY_FLEX_LAST_COLUMN);

        // 表格选择事件
        externalAppsTable.getSelectionModel().selectedItemProperty().addListener((observable, oldValue, newValue) -> {
            boolean hasSelection = newValue != null;
            editAppButton.setDisable(!hasSelection);
            deleteAppButton.setDisable(!hasSelection);
            launchAppButton.setDisable(!hasSelection);
        });

        logger.info("外部应用程序表格初始化完成");
    }

    /**
     * 加载外部应用程序
     */
    private void loadExternalApps() {
        List<ExternalApp> apps = configManager.getExternalApps();
        logger.info("加载外部应用程序，共{}个应用", apps.size());
        externalAppsList.clear();
        externalAppsList.addAll(apps);

        // 刷新表格显示
        Platform.runLater(() -> {
            externalAppsTable.refresh();
        });
    }

    /**
     * 加载配置文件信息
     */
    private void loadConfigFileInfo() {
        try {
            String configPath = configManager.getConfigFilePath();
            java.io.File configFile = new java.io.File(configPath);

            configPathLabel.setText("配置文件路径: " + configPath);

            if (configFile.exists()) {
                long fileSize = configFile.length();
                String sizeText = formatFileSize(fileSize);
                configSizeLabel.setText("文件大小: " + sizeText);

                java.util.Date lastModified = new java.util.Date(configFile.lastModified());
                configModifiedLabel.setText("最后修改: " + lastModified);
            } else {
                configSizeLabel.setText("文件大小: 文件不存在");
                configModifiedLabel.setText("最后修改: 未知");
            }

        } catch (Exception e) {
            logger.error("加载配置文件信息失败", e);
        }
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        }
    }

    /**
     * 新增外部应用程序
     */
    private void addExternalApp() {
        Stage currentStage = (Stage) cancelButton.getScene().getWindow();
        ExternalAppEditDialog dialog = new ExternalAppEditDialog(null, currentStage);
        if (dialog.showAndWait()) {
            ExternalApp newApp = dialog.getExternalApp();
            externalAppsList.add(newApp);

            // 刷新表格显示
            externalAppsTable.refresh();

            // 选中新添加的应用
            externalAppsTable.getSelectionModel().select(newApp);

            showStatus("应用程序添加成功", true);
            logger.info("新增外部应用程序: {}", newApp);
        }
    }

    /**
     * 编辑外部应用程序
     */
    private void editExternalApp() {
        ExternalApp selectedApp = externalAppsTable.getSelectionModel().getSelectedItem();
        if (selectedApp != null) {
            Stage currentStage = (Stage) cancelButton.getScene().getWindow();
            ExternalAppEditDialog dialog = new ExternalAppEditDialog(selectedApp, currentStage);
            if (dialog.showAndWait()) {
                // 刷新表格显示
                externalAppsTable.refresh();
                showStatus("应用程序编辑成功", true);
            }
        }
    }

    /**
     * 删除外部应用程序
     */
    private void deleteExternalApp() {
        ExternalApp selectedApp = externalAppsTable.getSelectionModel().getSelectedItem();
        if (selectedApp != null) {
            Alert alert = createAlert(Alert.AlertType.CONFIRMATION);
            alert.setTitle("确认删除");
            alert.setHeaderText(null);
            alert.setContentText("确定要删除应用程序 \"" + selectedApp.getName() + "\" 吗？");

            Optional<ButtonType> result = alert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                externalAppsList.remove(selectedApp);
                showStatus("应用程序删除成功", true);
            }
        }
    }

    /**
     * 启动外部应用程序
     */
    private void launchExternalApp() {
        ExternalApp selectedApp = externalAppsTable.getSelectionModel().getSelectedItem();
        if (selectedApp != null) {
            launchAppButton.setDisable(true);
            launchAppButton.setText("启动中...");

            externalAppService.launchApp(selectedApp).thenAccept(success -> {
                Platform.runLater(() -> {
                    launchAppButton.setDisable(false);
                    launchAppButton.setText("启动应用");

                    if (success) {
                        showStatus("应用程序启动成功: " + selectedApp.getName(), true);
                    } else {
                        showStatus("应用程序启动失败: " + selectedApp.getName(), false);
                    }
                });
            });
        }
    }

    /**
     * 导出配置文件
     */
    private void exportConfig() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("导出配置文件");
        fileChooser.setInitialFileName("iot-config-export.json");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON文件", "*.json")
        );

        java.io.File file = fileChooser.showSaveDialog(exportConfigButton.getScene().getWindow());
        if (file != null) {
            boolean success = configManager.exportConfig(file.getAbsolutePath());
            showStatus(success ? "配置导出成功" : "配置导出失败", success);
        }
    }

    /**
     * 导入配置文件
     */
    private void importConfig() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("导入配置文件");
        fileChooser.getExtensionFilters().add(
            new FileChooser.ExtensionFilter("JSON文件", "*.json")
        );

        java.io.File file = fileChooser.showOpenDialog(importConfigButton.getScene().getWindow());
        if (file != null) {
            Alert confirmAlert = createAlert(Alert.AlertType.CONFIRMATION);
            confirmAlert.setTitle("确认导入");
            confirmAlert.setHeaderText("导入配置文件");
            confirmAlert.setContentText("导入配置将覆盖当前所有设置，是否继续？");

            Optional<ButtonType> result = confirmAlert.showAndWait();
            if (result.isPresent() && result.get() == ButtonType.OK) {
                boolean success = configManager.importConfig(file.getAbsolutePath());
                if (success) {
                    showStatus("配置导入成功，请重新打开设置界面", true);
                    // 重新加载所有配置
                    loadCurrentConfig();
                    loadFormFields();
                    loadExternalApps();
                    loadConfigFileInfo();
                } else {
                    showStatus("配置导入失败", false);
                }
            }
        }
    }

    /**
     * 重置配置文件
     */
    private void resetConfig() {
        Alert confirmAlert = createAlert(Alert.AlertType.CONFIRMATION);
        confirmAlert.setTitle("确认重置");
        confirmAlert.setHeaderText("重置配置文件");
        confirmAlert.setContentText("重置将删除所有配置并恢复默认设置，是否继续？");

        Optional<ButtonType> result = confirmAlert.showAndWait();
        if (result.isPresent() && result.get() == ButtonType.OK) {
            try {
                // 先备份当前配置
                configManager.autoBackupConfig();

                // 删除配置文件
                java.io.File configFile = new java.io.File(configManager.getConfigFilePath());
                if (configFile.exists()) {
                    configFile.delete();
                }

                // 重新创建默认配置
                configManager = ConfigManager.getInstance();

                showStatus("配置重置成功，请重新打开设置界面", true);

                // 重新加载所有配置
                loadCurrentConfig();
                loadFormFields();
                loadExternalApps();
                loadConfigFileInfo();

            } catch (Exception e) {
                logger.error("重置配置失败", e);
                showStatus("重置配置失败: " + e.getMessage(), false);
            }
        }
    }

    /**
     * 下载模板到本地
     */
    @FXML
    private void downloadTemplate() {
        String templateUrl = templateDownloadUrlField.getText().trim();
        if (templateUrl.isEmpty()) {
            showStatus("请先输入模板下载地址", false);
            return;
        }

        progressIndicator.setVisible(true);
        downloadTemplateButton.setDisable(true);
        showStatus("正在下载模板...", true);

        // 创建下载任务
        Task<String> downloadTask = new Task<String>() {
            @Override
            protected String call() throws Exception {
                logger.info("开始直接下载模板，下载地址: {}", templateUrl);

                try {
                    // 创建模板服务
                    com.logictrue.service.TemplateService templateService = new com.logictrue.service.TemplateService();

                    updateMessage("正在下载模板文件...");

                    // 直接使用配置的下载地址进行下载
                    String templateFilePath = templateService.downloadTemplateFromUrl(templateUrl).get();

                    if (templateFilePath != null) {
                        logger.info("模板下载成功，保存路径: {}", templateFilePath);
                        return "模板下载成功\n保存路径: " + templateFilePath;
                    } else {
                        logger.error("模板下载失败");
                        return "模板下载失败";
                    }

                } catch (Exception e) {
                    logger.error("下载模板过程中发生异常", e);
                    return "下载异常: " + e.getMessage();
                }
            }
        };

        downloadTask.setOnSucceeded(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadTemplateButton.setDisable(false);

                String result = downloadTask.getValue();
                boolean success = result.contains("下载成功");
                showStatus(result, success);

                if (success) {
                    logger.info("模板下载任务完成: {}", result);
                    // 可以选择显示详细信息对话框
                    showTemplateDownloadResult(result);
                }
            });
        });

        downloadTask.setOnFailed(event -> {
            Platform.runLater(() -> {
                progressIndicator.setVisible(false);
                downloadTemplateButton.setDisable(false);

                Throwable exception = downloadTask.getException();
                String errorMessage = exception != null ? exception.getMessage() : "未知错误";
                logger.error("模板下载任务失败: {}", errorMessage, exception);
                showStatus("模板下载失败: " + errorMessage, false);
            });
        });

        // 监听进度消息更新
        downloadTask.messageProperty().addListener((obs, oldMessage, newMessage) -> {
            if (newMessage != null && !newMessage.isEmpty()) {
                Platform.runLater(() -> showStatus(newMessage, true));
            }
        });

        Thread downloadThread = new Thread(downloadTask);
        downloadThread.setDaemon(true);
        downloadThread.start();
    }

    /**
     * 显示模板下载结果详情
     */
    private void showTemplateDownloadResult(String result) {
        try {
            Alert alert = createAlert(Alert.AlertType.INFORMATION);
            alert.setTitle("模板下载成功");
            alert.setHeaderText("模板已成功下载到本地");
            alert.setContentText(result);

            // 设置对话框大小
            alert.getDialogPane().setPrefWidth(500);
            alert.getDialogPane().setPrefHeight(200);

            alert.showAndWait();
        } catch (Exception e) {
            logger.error("显示下载结果对话框失败", e);
        }
    }

    /**
     * 浏览Excel采集路径
     */
    @FXML
    private void browseExcelCollectionPath() {
        javafx.stage.DirectoryChooser directoryChooser = new javafx.stage.DirectoryChooser();
        directoryChooser.setTitle("选择Excel文件采集路径");

        // 设置初始目录
        String currentPath = excelCollectionPathField.getText().trim();
        if (!currentPath.isEmpty()) {
            java.io.File currentDir = new java.io.File(currentPath);
            if (currentDir.exists() && currentDir.isDirectory()) {
                directoryChooser.setInitialDirectory(currentDir);
            }
        }

        java.io.File selectedDirectory = directoryChooser.showDialog(browseExcelPathButton.getScene().getWindow());
        if (selectedDirectory != null) {
            excelCollectionPathField.setText(selectedDirectory.getAbsolutePath());
            showStatus("已选择Excel采集路径: " + selectedDirectory.getAbsolutePath(), true);
        }
    }

    /**
     * 设置主控制器引用
     */
    public void setMainController(MainController mainController) {
        this.mainController = mainController;
    }

    /**
     * 获取当前窗口作为所有者窗口
     */
    private Stage getOwnerWindow() {
        return (Stage) cancelButton.getScene().getWindow();
    }

    /**
     * 创建带有正确所有者窗口的Alert对话框
     * @deprecated 建议使用AlertUtil工具类
     */
    @Deprecated
    private Alert createAlert(Alert.AlertType alertType) {
        Alert alert = new Alert(alertType);
        // 设置所有者窗口为当前设置窗口
        Stage currentStage = getOwnerWindow();
        alert.initOwner(currentStage);
        // 确保Alert始终在设置窗口上方
        alert.initModality(Modality.WINDOW_MODAL);
        return alert;
    }

    /**
     * 显示Alert对话框（兼容旧代码）
     */
    private void showAlert(String title, String message) {
        AlertUtil.showAutoTypeAlert(title, message, getOwnerWindow());
    }
}
